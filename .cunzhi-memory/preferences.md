# 用户偏好设置

- 文档使用中文编写
- 用户明确要求：不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 帖子列表缓存优化策略：只缓存第一页，第一页缓存时间更长，因为大部分用户只看第一页
- 新增的文件添加到 git的 vcs
- 不更新created_at 和 updated_at
- 不是根据代码来生成百分百通过的测试用例，而是把函数当做黑盒，考虑各种边界条件
- "禁止使用包含\"401\"的错误码，由于前端对401进行模糊匹配，任何包含连续\"401\"字符串的错误码都可能被误匹配，因此禁止使用 XXX401 等可能包含401的格式"
- 用户明确要求：不要生成总结性Markdown文档、不要生成测试脚本、不要编译、不要运行，用户自己处理这些操作
- 用户偏好：不要生成总结性Markdown文档，不要生成测试脚本，不要编译（用户自己编译），不要运行（用户自己运行）
- invite_reward模块的GetNewUserMap调用不需要修改，保持原样
- 用户明确要求：不要生成总结性Markdown文档、不要生成测试脚本、帮我编译、不要运行（用户自己运行）
