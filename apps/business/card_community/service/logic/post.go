package logic

import (
	"context"
	"encoding/json"
	"errors"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// ValidatePostParams 验证帖子参数
func ValidatePostParams(description string, price int64) error {
	// 基本参数验证
	if description == "" {
		return define.CC500004Err
	}
	if price <= 0 {
		return define.CC500005Err
	}

	// 验证价格是否为10的倍数（即最多一位小数）
	// 因为价格以分为单位，一位小数对应10分的倍数
	if price%10 != 0 {
		return define.CC500006Err
	}

	return nil
}

// GetUserPostByID 获取用户的帖子（验证所有权）
func GetUserPostByID(ctx context.Context, userID, postID string) (*model.Post, error) {
	postSchema := repo.GetQuery().Post

	// 查询帖子是否存在且属于当前用户
	queryWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, postID).
		Eq(postSchema.MerchantID, userID).
		Build()

	post, err := repo.NewPostRepo(postSchema.WithContext(ctx)).SelectOne(queryWrapper)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500001Err // 帖子不存在
		}
		log.Ctx(ctx).Errorf("查询帖子失败: %v", err)
		return nil, commondefine.CommonErr
	}

	return post, nil
}

// ValidatePostEditable 验证帖子是否可编辑
func ValidatePostEditable(post *model.Post) error {
	// 检查帖子状态，违规下架的帖子不能编辑
	if post.GetStatus() == enums.PostStatusViolation {
		return define.CC500008Err.SetMsg("违规下架的帖子不能编辑")
	}

	// 检查帖子状态，已删除的帖子不能编辑
	if post.GetStatus() == enums.PostStatusDeleted {
		return define.CC500008Err.SetMsg("已删除的帖子不能编辑")
	}

	return nil
}

// 验证帖子是否可上下架
func ValidatePostEditableStatus(post *model.Post) error {
	// 检查帖子状态，违规下架的帖子不能上下架
	if post.GetStatus() == enums.PostStatusViolation {
		return define.CC500008Err.SetMsg("违规下架的帖子不能上下架")
	}

	// 检查帖子状态，已删除的帖子不能上下架
	if post.GetStatus() == enums.PostStatusDeleted {
		return define.CC500008Err.SetMsg("已删除的帖子不能上下架")
	}

	return nil
}

// UpdatePostStatusInternal 内部方法：更新帖子状态
func UpdatePostStatusInternal(ctx context.Context, userID, postID string, status enums.PostStatus) error {
	// 获取用户帖子
	post, err := GetUserPostByID(ctx, userID, postID)
	if err != nil {
		return err
	}

	// 检查状态是否有效
	if !status.IsValid() {
		return define.CC500002Err
	}

	// 用户不允许将自己的帖子状态变为违规下架
	if status == enums.PostStatusViolation {
		return define.CC500003Err.SetMsg("用户不能将帖子状态设为违规下架")
	}

	// 已删除(-1)和违规下架(-3)的帖子不能变更状态
	currentStatus := post.GetStatus()
	if currentStatus == enums.PostStatusDeleted || currentStatus == enums.PostStatusViolation {
		return define.CC500010Err.SetMsg("已删除和违规下架的帖子不能变更状态")
	}

	// 更新状态
	postSchema := repo.GetQuery().Post
	updateData := map[string]any{
		"status": int32(status),
	}

	updateWrapper := search.NewQueryBuilder().
		Eq(postSchema.ID, postID).
		Eq(postSchema.MerchantID, userID).
		Build()

	err = repo.NewPostRepo(postSchema.WithContext(ctx)).UpdateField(updateData, updateWrapper)
	if err != nil {
		log.Ctx(ctx).Errorf("更新帖子状态失败: %v", err)
		return commondefine.CommonErr
	}

	// 清理缓存（确保状态更新后立即清理缓存）
	if err := InvalidatePostCache(ctx, postID); err != nil {
		log.Ctx(ctx).Warnf("清理帖子缓存失败: %v", err)
	}

	return nil
}

// SetPostDetailCache 设置帖子详情缓存
func SetPostDetailCache(ctx context.Context, postID string, postDetail *define.GetPostDetailResp) error {
	jsonData, err := json.Marshal(postDetail)
	if err != nil {
		log.Ctx(ctx).Errorf("序列化帖子详情缓存数据失败 postID:%s, err:%v", postID, err)
		return err
	}

	cacheKey := constant.GetPostDetailKey(postID)
	if _, err := global.REDIS.Set(ctx, cacheKey, jsonData, constant.PostDetailTTL).Result(); err != nil {
		log.Ctx(ctx).Errorf("设置帖子详情缓存失败 postID:%s, err:%v", postID, err)
		return err
	}

	return nil
}

// SetPostListCache 设置帖子列表缓存（只缓存第一页）
func SetPostListCache(ctx context.Context, page, size int, postList *define.GetPostListResp) error {
	// 只缓存第一页
	if page != 1 {
		return nil
	}

	jsonData, err := json.Marshal(postList)
	if err != nil {
		log.Ctx(ctx).Errorf("序列化帖子列表缓存数据失败 page:%d, size:%d, err:%v", page, size, err)
		return err
	}

	cacheKey := constant.GetPostListKey(page, size)
	// 第一页使用更长的缓存时间
	ttl := constant.PostListFirstTTL
	if _, err := global.REDIS.Set(ctx, cacheKey, jsonData, ttl).Result(); err != nil {
		log.Ctx(ctx).Errorf("设置帖子列表缓存失败 page:%d, size:%d, err:%v", page, size, err)
		return err
	}

	return nil
}

// InvalidatePostCache 失效帖子相关缓存
func InvalidatePostCache(ctx context.Context, postID string) error {
	// 删除帖子详情缓存
	cacheKey := constant.GetPostDetailKey(postID)
	if err := global.REDIS.Del(ctx, cacheKey).Err(); err != nil {
		log.Ctx(ctx).Errorf("删除帖子详情缓存失败 postID:%s, err:%v", postID, err)
		return err
	}

	// 删除帖子列表缓存（只删除第一页缓存，因为只缓存第一页）
	listPattern := "app_service:card_community:post:list:page:1:*"
	keys, err := global.REDIS.Keys(ctx, listPattern).Result()
	if err != nil {
		log.Ctx(ctx).Errorf("获取帖子列表缓存键失败 pattern:%s, err:%v", listPattern, err)
		return err
	}

	if len(keys) > 0 {
		if err := global.REDIS.Del(ctx, keys...).Err(); err != nil {
			log.Ctx(ctx).Errorf("删除帖子列表缓存失败 keys:%v, err:%v", keys, err)
			return err
		}
	}

	return nil
}

// GetPostDetailFromCache 从缓存获取帖子详情
func GetPostDetailFromCache(ctx context.Context, postID string) (*define.GetPostDetailResp, error) {
	cacheKey := constant.GetPostDetailKey(postID)
	result, err := global.REDIS.Get(ctx, cacheKey).Result()

	switch {
	case err == nil:
		// 缓存命中，反序列化数据
		var postDetail define.GetPostDetailResp
		if err := json.Unmarshal([]byte(result), &postDetail); err != nil {
			log.Ctx(ctx).Errorf("解析帖子详情缓存数据失败 postID:%s, err:%v", postID, err)
			return nil, err
		}
		return &postDetail, nil

	case errors.Is(err, redis.Nil):
		// 缓存未命中
		return nil, nil

	default:
		log.Ctx(ctx).Errorf("获取帖子详情缓存失败 postID:%s, err:%v", postID, err)
		return nil, err
	}
}

// GetPostListFromCache 从缓存获取帖子列表（只缓存第一页）
func GetPostListFromCache(ctx context.Context, page, size int) (*define.GetPostListResp, error) {
	// 只缓存第一页
	if page != 1 {
		return nil, nil
	}

	cacheKey := constant.GetPostListKey(page, size)
	result, err := global.REDIS.Get(ctx, cacheKey).Result()

	switch {
	case err == nil:
		// 缓存命中，反序列化数据
		var postList define.GetPostListResp
		if err := json.Unmarshal([]byte(result), &postList); err != nil {
			log.Ctx(ctx).Errorf("解析帖子列表缓存数据失败 page:%d, size:%d, err:%v", page, size, err)
			return nil, err
		}
		return &postList, nil

	case errors.Is(err, redis.Nil):
		// 缓存未命中
		return nil, nil

	default:
		log.Ctx(ctx).Errorf("获取帖子列表缓存失败 page:%d, size:%d, err:%v", page, size, err)
		return nil, err
	}
}
